#!/usr/bin/env python3
"""
手动触发索引任务脚本
用于手动启动连接器的索引任务，绕过自动调度机制
"""

import os
import sys
import time
from typing import Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from onyx.db.engine import get_session_with_current_tenant
from onyx.db.connector_credential_pair import get_connector_credential_pairs
from onyx.db.index_attempt import get_latest_index_attempt
from onyx.background.celery.celery_app import celery_app
from onyx.background.celery.tasks.shared import OnyxCeleryTask, OnyxCeleryPriority
from onyx.utils.logger import setup_logger

logger = setup_logger()

def list_connector_credential_pairs():
    """列出所有连接器凭证对"""
    print("=== 连接器凭证对列表 ===")
    
    with get_session_with_current_tenant() as db_session:
        cc_pairs = get_connector_credential_pairs(db_session)
        
        if not cc_pairs:
            print("❌ 没有找到任何连接器凭证对")
            return []
        
        print(f"📋 找到 {len(cc_pairs)} 个连接器凭证对:")
        for cc_pair in cc_pairs:
            connector = cc_pair.connector
            credential = cc_pair.credential
            
            # 获取最新的索引尝试
            latest_attempt = get_latest_index_attempt(
                connector_credential_pair_id=cc_pair.id,
                db_session=db_session
            )
            
            status = "无索引记录"
            if latest_attempt:
                status = latest_attempt.status.value
            
            print(f"  ID: {cc_pair.id}")
            print(f"  连接器: {connector.name} ({connector.source.value})")
            print(f"  凭证: {credential.credential_json.get('username', 'N/A')}")
            print(f"  状态: {status}")
            print(f"  最后更新: {cc_pair.last_successful_index_time or '从未'}")
            print("-" * 40)
        
        return cc_pairs

def get_cc_pair_status(cc_pair_id: int):
    """获取特定连接器凭证对的详细状态"""
    with get_session_with_current_tenant() as db_session:
        cc_pairs = get_connector_credential_pairs(db_session)
        cc_pair = next((cp for cp in cc_pairs if cp.id == cc_pair_id), None)
        
        if not cc_pair:
            print(f"❌ 未找到ID为 {cc_pair_id} 的连接器凭证对")
            return None
        
        print(f"=== 连接器凭证对 {cc_pair_id} 详细状态 ===")
        print(f"连接器名称: {cc_pair.connector.name}")
        print(f"连接器类型: {cc_pair.connector.source.value}")
        print(f"连接器配置: {cc_pair.connector.connector_specific_config}")
        print(f"凭证用户: {cc_pair.credential.credential_json.get('username', 'N/A')}")
        print(f"是否启用: {'是' if not cc_pair.connector.disabled else '否'}")
        print(f"最后成功索引: {cc_pair.last_successful_index_time or '从未'}")
        
        # 获取最近的索引尝试
        latest_attempt = get_latest_index_attempt(
            connector_credential_pair_id=cc_pair_id,
            db_session=db_session
        )
        
        if latest_attempt:
            print(f"最新索引尝试:")
            print(f"  ID: {latest_attempt.id}")
            print(f"  状态: {latest_attempt.status.value}")
            print(f"  开始时间: {latest_attempt.time_started or '未开始'}")
            print(f"  完成时间: {latest_attempt.time_finished or '未完成'}")
            print(f"  错误信息: {latest_attempt.error_msg or '无'}")
            print(f"  Celery任务ID: {latest_attempt.celery_task_id or '无'}")
        else:
            print("无索引尝试记录")
        
        return cc_pair

def manual_trigger_indexing(cc_pair_id: int, from_beginning: bool = False):
    """手动触发索引任务"""
    print(f"=== 手动触发索引任务 ===")
    print(f"连接器凭证对ID: {cc_pair_id}")
    print(f"从头开始: {'是' if from_beginning else '否'}")
    
    try:
        # 发送检查索引任务
        print("📤 发送 CHECK_FOR_INDEXING 任务...")
        result = celery_app.send_task(
            OnyxCeleryTask.CHECK_FOR_INDEXING,
            priority=OnyxCeleryPriority.HIGHEST,
            kwargs={"tenant_id": "public"}  # 默认租户
        )
        
        if result:
            print(f"✅ 任务已发送，任务ID: {result.id}")
            print("⏳ 等待任务执行...")
            
            # 等待一段时间让任务执行
            time.sleep(5)
            
            # 检查任务状态
            task_result = result.get(timeout=30)
            print(f"📊 任务执行结果: {task_result}")
            
        else:
            print("❌ 任务发送失败")
            
    except Exception as e:
        print(f"❌ 触发索引任务时出错: {e}")
        logger.exception("手动触发索引任务失败")

def check_celery_status():
    """检查Celery状态"""
    print("=== Celery状态检查 ===")
    
    try:
        # 检查Celery是否可用
        inspect = celery_app.control.inspect()
        
        # 获取活跃任务
        active_tasks = inspect.active()
        if active_tasks:
            print("🔄 活跃任务:")
            for worker, tasks in active_tasks.items():
                print(f"  Worker: {worker}")
                for task in tasks:
                    print(f"    任务: {task.get('name', 'Unknown')}")
                    print(f"    ID: {task.get('id', 'Unknown')}")
        else:
            print("📭 没有活跃任务")
        
        # 获取已注册的任务
        registered_tasks = inspect.registered()
        if registered_tasks:
            print("\n📋 已注册的任务:")
            for worker, tasks in registered_tasks.items():
                print(f"  Worker: {worker}")
                for task in tasks[:5]:  # 只显示前5个
                    print(f"    {task}")
                if len(tasks) > 5:
                    print(f"    ... 还有 {len(tasks) - 5} 个任务")
        
        # 获取统计信息
        stats = inspect.stats()
        if stats:
            print("\n📊 Worker统计:")
            for worker, stat in stats.items():
                print(f"  Worker: {worker}")
                print(f"    状态: {'在线' if stat else '离线'}")
                if stat:
                    print(f"    进程数: {stat.get('pool', {}).get('processes', 'Unknown')}")
                    print(f"    总任务数: {stat.get('total', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ 检查Celery状态时出错: {e}")
        logger.exception("检查Celery状态失败")

def main():
    """主函数"""
    print("🚀 Onyx手动索引触发工具")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python manual_trigger_indexing.py list                    # 列出所有连接器")
        print("  python manual_trigger_indexing.py status <cc_pair_id>     # 查看连接器状态")
        print("  python manual_trigger_indexing.py trigger <cc_pair_id>    # 触发索引")
        print("  python manual_trigger_indexing.py celery                  # 检查Celery状态")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == "list":
            list_connector_credential_pairs()
            
        elif command == "status":
            if len(sys.argv) < 3:
                print("❌ 请提供连接器凭证对ID")
                return
            cc_pair_id = int(sys.argv[2])
            get_cc_pair_status(cc_pair_id)
            
        elif command == "trigger":
            if len(sys.argv) < 3:
                print("❌ 请提供连接器凭证对ID")
                return
            cc_pair_id = int(sys.argv[2])
            from_beginning = len(sys.argv) > 3 and sys.argv[3].lower() == "full"
            manual_trigger_indexing(cc_pair_id, from_beginning)
            
        elif command == "celery":
            check_celery_status()
            
        else:
            print(f"❌ 未知命令: {command}")
            
    except ValueError as e:
        print(f"❌ 参数错误: {e}")
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        logger.exception("执行命令失败")

if __name__ == "__main__":
    main()
